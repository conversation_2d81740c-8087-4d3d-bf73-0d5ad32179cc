### Goal
Make SettingsScreen smaller and easier to reason about by moving state collection (collectAsState) and initial-load effects into the individual tab composables, while keeping the existing presentational tabs (ProvidersTab, ModelsTab, SettingsConfigTab) clean and testable.

### High‑level approach (Route + Presentational pattern)
- Keep ProvidersTab, ModelsTab, SettingsConfigTab as presentational components that render based on a simple `State + Actions` API.
- Introduce one “Route” composable per tab:
  - ProvidersTabRoute
  - ModelsTabRoute
  - SettingsConfigTabRoute
- Each Route composable:
  - Acquires its ViewModel via `koinViewModel()`.
  - Performs `collectAsState()` on the ViewModel’s flows.
  - Runs the initial `LaunchedEffect` for that tab only (e.g., loadProviders, loadModelsAndProviders, loadModels).
  - Constructs the `…TabState` and `…TabActions` objects.
  - Calls the presentational tab with those.
- Update SettingsScreen to:
  - Only manage `selectedTabIndex` and the TabRow UI.
  - Render the Route composables instead of wiring all view models and states centrally.

This isolates each tab’s data lifecycle and removes the large block of collectors and action wiring from SettingsScreen.

---

### Step‑by‑step refactor plan

#### 1) Create ProvidersTabRoute
Add a new composable next to ProvidersTab.kt (same package) that wires VM → state/actions → ProvidersTab.

```kotlin
@Composable
fun ProvidersTabRoute(
    viewModel: ProviderConfigViewModel = koinViewModel()
) {
    // Tab‑local initial load
    LaunchedEffect(Unit) {
        viewModel.loadProviders()
    }

    // Collect tab state here
    val providersState by viewModel.providersState.collectAsState()
    val selectedProvider by viewModel.selectedProvider.collectAsState()
    val dialogState by viewModel.dialogState.collectAsState()

    // Build presentational state
    val state = ProvidersTabState(
        providersUiState = providersState,
        selectedProvider = selectedProvider,
        dialogState = dialogState
    )

    // Build actions forwarding to VM
    val actions = object : ProvidersTabActions {
        override fun onLoadProviders() = viewModel.loadProviders()
        override fun onSelectProvider(provider: LLMProvider?) = viewModel.selectProvider(provider)
        override fun onStartAddingNewProvider() = viewModel.startAddingNewProvider()
        override fun onCancelDialog() = viewModel.cancelDialog()
        override fun onSaveProvider() = viewModel.saveProvider()
        override fun onStartEditingProvider(provider: LLMProvider) = viewModel.startEditingProvider(provider)
        override fun onStartDeletingProvider(provider: LLMProvider) = viewModel.startDeletingProvider(provider)
        override fun onDeleteProvider(providerId: Long) = viewModel.deleteProvider(providerId)
        override fun onUpdateProviderCredential() = viewModel.updateProviderCredential()
        override fun onUpdateProviderForm(update: (ProviderFormState) -> ProviderFormState) =
            viewModel.updateProviderForm(update)
    }

    ProvidersTab(state = state, actions = actions)
}
```

Notes:
- The presentational ProvidersTab remains unchanged.
- ProvidersTabRoute handles both the initial load and all state collection.

#### 2) Create ModelsTabRoute
```kotlin
@Composable
fun ModelsTabRoute(
    viewModel: ModelConfigViewModel = koinViewModel()
) {
    LaunchedEffect(Unit) {
        viewModel.loadModelsAndProviders()
    }

    val modelConfigState by viewModel.modelConfigState.collectAsState()
    val selectedModel by viewModel.selectedModel.collectAsState()
    val dialogState by viewModel.dialogState.collectAsState()

    val state = ModelsTabState(
        modelConfigUiState = modelConfigState,
        selectedModel = selectedModel,
        dialogState = dialogState
    )

    val actions = object : ModelsTabActions {
        override fun onLoadModelsAndProviders() = viewModel.loadModelsAndProviders()
        override fun onStartAddingNewModel() = viewModel.startAddingNewModel()
        override fun onSaveModel() = viewModel.saveModel()
        override fun onStartEditingModel(model: LLMModel) = viewModel.startEditingModel(model)
        override fun onStartDeletingModel(model: LLMModel) = viewModel.startDeletingModel(model)
        override fun onDeleteModel(modelId: Long) = viewModel.deleteModel(modelId)
        override fun onSelectModel(model: LLMModel?) = viewModel.selectModel(model)
        override fun onUpdateModelForm(update: (ModelFormState) -> ModelFormState) = viewModel.updateModelForm(update)
        override fun onCancelDialog() = viewModel.cancelDialog()
    }

    ModelsTab(state = state, actions = actions)
}
```

#### 3) Create SettingsConfigTabRoute
```kotlin
@Composable
fun SettingsConfigTabRoute(
    viewModel: SettingsConfigViewModel = koinViewModel()
) {
    LaunchedEffect(Unit) {
        viewModel.loadModels()
    }

    val selectedModel by viewModel.selectedModel.collectAsState()
    val selectedSettings by viewModel.selectedSettings.collectAsState()
    val settingsConfigState by viewModel.settingsConfigState.collectAsState()
    val dialogState by viewModel.dialogState.collectAsState()

    val state = SettingsConfigTabState(
        settingsConfigState = settingsConfigState,
        selectedModel = selectedModel,
        selectedSettings = selectedSettings,
        dialogState = dialogState
    )

    val actions = object : SettingsConfigTabActions {
        override fun onLoadModels() = viewModel.loadModels()
        // Add more actions later as Settings UI grows
    }

    SettingsConfigTab(state = state, actions = actions)
}
```

#### 4) Simplify SettingsScreen
- Remove all `collectAsState()` calls and action/state wiring.
- Keep only the TabRow and render the new Route composables.
- Optionally, keep `selectedTabIndex` in `rememberSaveable` so the selected tab survives process restores.

Before (key lines):
- Collections: lines 49–61
- State object creation: lines 64–81
- Actions creation: lines 84–118
- Initial load: lines 43–47

After (sketch):
```kotlin
@Composable
fun SettingsScreen() {
    var selectedTabIndex by rememberSaveable { mutableIntStateOf(0) }
    val tabTitles = listOf("Providers", "Models", "Settings")

    Column(Modifier.fillMaxSize().background(MaterialTheme.colorScheme.background)) {
        TabRow(selectedTabIndex = selectedTabIndex, modifier = Modifier.fillMaxWidth()) {
            tabTitles.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTabIndex == index,
                    onClick = { selectedTabIndex = index },
                    text = { Text(title, style = MaterialTheme.typography.titleMedium) }
                )
            }
        }

        Box(Modifier.fillMaxSize().padding(16.dp)) {
            when (selectedTabIndex) {
                0 -> ProvidersTabRoute()
                1 -> ModelsTabRoute()
                2 -> SettingsConfigTabRoute()
            }
        }
    }
}
```

This removes all cross‑tab coupling from SettingsScreen and ensures each tab loads and collects its own data only when displayed.

---

### Optional variations
- Inject ViewModels from SettingsScreen if you prefer a single DI point: pass them into the Route composables; the Routes still handle collection and effects.
- If you want to avoid composing initial loads multiple times when users switch tabs, the default behavior is already fine because ViewModels survive recompositions; however, you can gate your `LaunchedEffect` with a `rememberSaveable` boolean flag if any of your load calls must be truly “once per app session.”

```kotlin
val loaded by rememberSaveable { mutableStateOf(false) }
LaunchedEffect(loaded) {
    if (!loaded) {
        viewModel.loadProviders()
        loaded = true
    }
}
```

### Recomposition and performance tips
- Collect the smallest necessary slices from the ViewModel to avoid broad recompositions. If a `UiState.Success` wraps a large data object, consider deriving smaller memoized values with `remember(uiState) { … }` or `derivedStateOf { … }`.
- Keep the presentational tabs stateless with respect to ViewModels; that makes previews and unit tests straightforward.

### Migration checklist
1. Add ProvidersTabRoute, ModelsTabRoute, SettingsConfigTabRoute as shown.
2. Replace the tab bodies in SettingsScreen with the Route composables.
3. Remove the global `LaunchedEffect` and all collectors/actions/state construction from SettingsScreen.
4. Verify that each tab still behaves identically (loading, error, success, dialogs, FABs).
5. Run desktop tests for the app module if present: `./gradlew app:desktopTest`.
6. Manually verify tab switching; ensure state persists due to ViewModel scope.

### Future extensions
- If SettingsConfig grows, follow the same pattern: keep a presentational `SettingsConfigTab` and a `SettingsConfigTabRoute` to encapsulate ViewModel logic.
- If tabs start sharing small cross‑concerns (e.g., a global snackbar), lift only those concerns back to SettingsScreen or a parent scaffold, not the primary data flows.

### Summary
By introducing tab‑scoped Route composables that own state collection and initial loading, you substantially simplify SettingsScreen and align each tab with single‑responsibility. The presentational tabs remain clean and reusable, while the Routes encapsulate ViewModel wiring and effects per feature area.