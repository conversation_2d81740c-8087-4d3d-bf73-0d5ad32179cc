## **Refactoring Plan: Unifying Session and Chat Repositories**

### **1. Objective**

The goal of this refactoring is to establish the `SessionRepository` as the **single source of truth for all chat session data, including its messages**. We will eliminate the redundant `ChatRepository` and encapsulate all message-related business logic (including streaming) within the `SessionRepository`.

This will dramatically simplify our architecture, remove data duplication, and make our state management purely reactive and much more robust.

### **2. The New Architecture**

1.  **`SessionRepository` is King:** It will be the only repository responsible for fetching, caching, and modifying `ChatSession` objects and their internal `messages` list.
2.  **`ChatRepository` is Deleted:** Its responsibilities will be fully merged into `SessionRepository`.
3.  **Streaming Logic is Encapsulated:** The complex logic for handling streaming events, currently in `StreamingCoordinator`, will be moved *inside* the `DefaultSessionRepository`. The repository will handle the stream and update its internal cache, hiding the complexity from the rest of the app.
4.  **`StreamingCoordinator` is Deleted:** The `SendMessageUseCase` will now directly call the new streaming method on the `SessionRepository` and will no longer need this coordinator.
5.  **Use Cases become Simpler:** They will only call methods on `SessionRepository` and will **never** manually update the `ChatState`'s message list.

### **3. Step-by-Step Refactoring Guide**

#### **Step 1: Merge `ChatRepository` into `SessionRepository`**

We will move all methods from `ChatRepository` into `SessionRepository` and adapt them to work with the cached `ChatSession` objects.

**Action Items:**

1.  **Update the `SessionRepository` interface:**
    *   Add the following methods from `ChatRepository`'s interface:
        ```kotlin
        // Add these to SessionRepository.kt
        suspend fun processNewMessage(sessionId: Long, request: ProcessNewMessageRequest): Either<RepositoryError, Unit>
        fun processNewMessageStreaming(sessionId: Long, request: ProcessNewMessageRequest): Flow<Either<RepositoryError, ChatStreamEvent>>
        suspend fun updateMessageContent(messageId: Long, request: UpdateMessageRequest): Either<RepositoryError, Unit>
        suspend fun deleteMessage(messageId: Long): Either<RepositoryError, Unit>
        ```
    *   **Note the return type changes:** `processNewMessage`, `updateMessageContent`, and `deleteMessage` now return `Either<..., Unit>` because their only job is to update the repository's internal state. The UI will react to the `StateFlow` change, so we don't need to return the data from the method itself.

2.  **Implement the merged logic in `DefaultSessionRepository`:**
    *   Implement `processNewMessage`: It will call the API, and on success, it will find the cached `ChatSession` for the given `sessionId` and add the new messages to its `messages` list.
    *   Implement `updateMessageContent`: On success, it will find the relevant cached `ChatSession` and map over its `messages` list to replace the updated message.
    *   Implement `deleteMessage`: On success, it will find the relevant cached `ChatSession` and filter its `messages` list to remove the deleted message.

3.  **Delete `ChatRepository.kt` and `DefaultChatRepository.kt`:** These files are now fully redundant.

---

#### **Step 2: Encapsulate Streaming Logic and Delete `StreamingCoordinator`**

We will move the complex stateful logic of processing stream events from the `StreamingCoordinator` into the `DefaultSessionRepository`, where it can directly and safely modify the cached `ChatSession`.

**Action Items:**

1.  **Create a private helper function inside `DefaultSessionRepository`:**
    *   Create `private fun applyStreamEvent(sessionId: Long, event: ChatStreamEvent)`.
    *   Copy the entire `when (event)` block from `StreamingCoordinator.handleStreamingEvent` into this new function.
    *   Replace all calls to `state.updateSessionMessages(...)` and `state.updateSessionLeafId(...)` with direct modifications to the cached `ChatSession` object for the given `sessionId`.

2.  **Implement `processNewMessageStreaming` in `DefaultSessionRepository`:**
    *   This method will call the `chatApi`.
    *   It will use the `.onEach` operator on the resulting flow. Inside `onEach`, it will call the new `applyStreamEvent` helper to update the repository's internal state as events arrive.
    *   The flow itself simply passes the events through to the collector.

    **Example Implementation in `DefaultSessionRepository`:**
    ```kotlin
    override fun processNewMessageStreaming(sessionId: Long, request: ProcessNewMessageRequest): Flow<Either<RepositoryError, ChatStreamEvent>> {
        return chatApi.processNewMessageStreaming(sessionId, request)
            .onEach { either -> // Use onEach for the side-effect of updating the cache
                either.map { event ->
                    // This is the core logic: apply the event to our internal state
                    applyStreamEvent(sessionId, event)
                }
            }
            .map { it.mapLeft { err -> err.toRepositoryError("Streaming Error") } }
    }
    ```

3.  **Delete `StreamingCoordinator.kt`:** Its logic is now safely encapsulated in the repository.

---

#### **Step 3: Refactor All Use Cases**

Now, update all Use Cases to work with the new, unified `SessionRepository` and remove any manual state manipulation.

**Action Items:**

1.  **Refactor `SendMessageUseCase`:**
    *   Remove the `StreamingCoordinator` dependency and inject `SessionRepository`.
    *   In `execute()`, call `sessionRepository.processNewMessage(...)` or `sessionRepository.processNewMessageStreaming(...).collect()` based on the streaming setting.
    *   **Crucially, delete all manual state updates:** remove `state.setInputContent("")`, `state.setReplyTarget(null)`, etc., from this use case. These are now side effects of the stream events handled by the repository.

2.  **Refactor `EditMessageUseCase` and `DeleteMessageUseCase`:**
    *   Replace the `ChatRepository` dependency with `SessionRepository`.
    *   Update the method calls (e.g., `chatRepository.deleteMessage` -> `sessionRepository.deleteMessage`).
    *   **Delete all manual state updates and session reloads.** For example, in `DeleteMessageUseCase`, remove the call to `loadSessionUseCase.execute(...)`. The repository update is now atomic and reactive.

3.  **Refactor `SwitchBranchUseCase`:**
    *   This use case should already be using `sessionApi`. Let's move that logic into `SessionRepository.updateSessionLeafMessage`.
    *   The implementation of `updateSessionLeafMessage` inside `DefaultSessionRepository` should make the API call and, on success, update the `currentLeafMessageId` of the cached `ChatSession`.
    *   The use case will then simply call `sessionRepository.updateSessionLeafMessage(...)` and remove its own manual `state.updateSessionLeafId(...)` call.

---

#### **Step 4: Final Cleanup and Verification**

1.  **Update Dependency Injection:** Remove all DI bindings for `ChatRepository` and `StreamingCoordinator`. Ensure `SessionRepository` is correctly injected into the updated Use Cases.
2.  **Review `ChatState`:** Confirm that all methods for manually manipulating the message list (`updateSessionMessages`, `updateSessionLeafId`, `updateSessionModelId`, etc.) have been removed from the `SessionState` interface and `ChatStateImpl`. The state should only be changed by setting the `activeSessionId` or by user interaction state setters (`setInputContent`).
3.  **Review `ChatViewModel`:** The ViewModel should remain simple. It delegates actions to Use Cases and exposes state from `ChatState`. No changes should be needed here if the previous steps were done correctly.

By following this plan, you will achieve a clean, maintainable, and truly reactive architecture that is free from data duplication and race conditions.



## Notes
The original code for StreamingCoordinator is shown here for reference:

```kotlin
package eu.torvian.chatbot.app.viewmodel.chat.usecase

import eu.torvian.chatbot.app.generated.resources.Res
import eu.torvian.chatbot.app.generated.resources.error_sending_message_short
import eu.torvian.chatbot.app.repository.ChatRepository
import eu.torvian.chatbot.app.viewmodel.common.ErrorNotifier
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.app.viewmodel.chat.state.ChatState
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ChatStreamEvent
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import kotlinx.datetime.Clock

/**
 * Coordinates streaming message processing.
 * Handles the streaming flow lifecycle and updates ChatState accordingly.
 */
class StreamingCoordinator(
    private val chatRepository: ChatRepository,
    private val state: ChatState,
    private val errorNotifier: ErrorNotifier
) {

    private val logger = kmpLogger<StreamingCoordinator>()

    /**
     * Executes streaming message processing for the given session and content.
     *
     * @param currentSession The current chat session
     * @param content The message content to send
     * @param parentId The parent message ID for threading
     */
    suspend fun execute(currentSession: ChatSession, content: String, parentId: Long?) {
        logger.info("Starting streaming message for session ${currentSession.id}")

        chatRepository.processNewMessageStreaming(
            sessionId = currentSession.id,
            request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
        ).collect { eitherUpdate ->
            eitherUpdate.fold(
                ifLeft = { repositoryError ->
                    logger.error("Streaming message repository error: ${repositoryError.message}")
                    errorNotifier.repositoryError(
                        error = repositoryError,
                        shortMessageRes = Res.string.error_sending_message_short
                    )
                },
                ifRight = { chatUpdate ->
                    handleStreamingEvent(chatUpdate, currentSession)
                }
            )
        }
    }

    /**
     * Handles individual streaming events from the API.
     */
    private suspend fun handleStreamingEvent(event: ChatStreamEvent, currentSession: ChatSession) {
        if (state.currentSession?.id != currentSession.id) return
        when (event) {
            is ChatStreamEvent.UserMessageSaved -> {
                logger.debug("User message saved: ${event.message.id}")
                // Add the user message to the session's messages and update parent's children list
                state.currentSession?.let { session ->
                    val updatedMessages = session.messages.map {
                        if (it.id == event.message.parentMessageId) {
                            val newChildren = it.childrenMessageIds + event.message.id
                            val now = Clock.System.now()
                            when (it) {
                                is ChatMessage.UserMessage -> it.copy(
                                    childrenMessageIds = newChildren,
                                    updatedAt = now
                                )
                                is ChatMessage.AssistantMessage -> it.copy(
                                    childrenMessageIds = newChildren,
                                    updatedAt = now
                                )
                            }
                        } else {
                            it
                        }
                    } + event.message
                    state.updateSessionMessages(updatedMessages)
                    state.updateSessionLeafId(event.message.id)
                }
                // Clear input and reply target after user message is confirmed
                state.setInputContent("")
                state.setReplyTarget(null)
            }

            is ChatStreamEvent.AssistantMessageStart -> {
                logger.debug("Assistant message started: ${event.assistantMessage.id}")
                // Add the assistant message to the session's messages
                state.currentSession?.let { session ->
                    val updatedMessages = session.messages + event.assistantMessage
                    state.updateSessionMessages(updatedMessages)
                    state.updateSessionLeafId(event.assistantMessage.id)
                }
            }

            is ChatStreamEvent.AssistantMessageDelta -> {
                logger.trace("Assistant message delta: ${event.deltaContent.length} chars")
                // Update the streaming message content
                state.currentSession?.let { session ->
                    val updatedMessages = session.messages.map {
                        if (it.id == event.messageId) {
                            val newContent = it.content + event.deltaContent
                            val now = Clock.System.now()
                            (it as ChatMessage.AssistantMessage).copy(content = newContent, updatedAt = now)
                        } else {
                            it
                        }
                    }
                    state.updateSessionMessages(updatedMessages)
                }
            }

            is ChatStreamEvent.AssistantMessageEnd -> {
                logger.info("Assistant message completed: ${event.finalAssistantMessage.id}")
                // Remove the temporary messages from the session's messages and add the final ones
                state.currentSession?.let { session ->
                    val updatedMessages = session.messages.filter {
                        it.id != event.tempMessageId && it.id != event.finalUserMessage.id
                    } + event.finalUserMessage + event.finalAssistantMessage
                    val newLeafId = event.finalAssistantMessage.id
                    state.updateSessionMessages(updatedMessages)
                    state.updateSessionLeafId(newLeafId)
                }
            }

            is ChatStreamEvent.ErrorOccurred -> {
                logger.error("Streaming error: ${event.error.message}")
                errorNotifier.apiError(
                    error = event.error,
                    shortMessageRes = Res.string.error_sending_message_short
                )
            }

            ChatStreamEvent.StreamCompleted -> {
                logger.info("Streaming completed for session ${state.currentSession?.id}")
            }
        }
    }
}

```