package eu.torvian.chatbot.app.repository

import arrow.core.Either
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatStreamEvent
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import eu.torvian.chatbot.common.models.UpdateMessageRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * Repository interface for managing chat messages and streaming operations.
 *
 * This repository serves as the single source of truth for chat message data in the application,
 * providing reactive data streams through StateFlow and handling all chat-related operations.
 * It abstracts the underlying API layer and provides comprehensive error handling through
 * the RepositoryError hierarchy.
 *
 * The repository maintains an internal cache of message data organized by session and automatically
 * updates all observers when changes occur, ensuring data consistency across the application.
 */
interface ChatRepository {
    
    /**
     * Reactive stream of chat messages for a specific session.
     * 
     * This StateFlow provides real-time updates whenever the message data changes for the session,
     * allowing ViewModels and other consumers to automatically react to data changes
     * without manual refresh operations.
     *
     * @param sessionId The ID of the session to observe messages for
     * @return StateFlow containing the current state of messages for the session wrapped in DataState
     */
    fun getMessagesForSession(sessionId: Long): StateFlow<DataState<RepositoryError, List<ChatMessage>>>

    /**
     * Processes a new user message in a session and gets the LLM response (non-streaming).
     *
     * This operation sends the message to the backend and updates the internal StateFlow
     * with both the user message and the assistant's response.
     *
     * @param sessionId The ID of the session to send the message to
     * @param request The message processing request containing content and optional parent ID
     * @return Either.Right with the list of created messages on success, or Either.Left with RepositoryError on failure
     */
    suspend fun processNewMessage(sessionId: Long, request: ProcessNewMessageRequest): Either<RepositoryError, List<ChatMessage>>

    /**
     * Processes a new user message in a session with streaming LLM response.
     *
     * This operation sends the message to the backend and returns a Flow of streaming events.
     * The repository will update its internal StateFlow as the streaming progresses.
     *
     * @param sessionId The ID of the session to send the message to
     * @param request The message processing request containing content and optional parent ID
     * @return Flow of Either containing ChatStreamEvent updates or RepositoryError on failure
     */
    fun processNewMessageStreaming(sessionId: Long, request: ProcessNewMessageRequest): Flow<Either<RepositoryError, ChatStreamEvent>>

    /**
     * Updates the content of an existing message.
     *
     * Upon successful update, the modified message replaces the existing one in the
     * internal StateFlow, triggering updates to all observers.
     *
     * @param messageId The unique identifier of the message to update
     * @param request The update request containing the new content
     * @return Either.Right with the updated ChatMessage on success, or Either.Left with RepositoryError on failure
     */
    suspend fun updateMessageContent(messageId: Long, request: UpdateMessageRequest): Either<RepositoryError, ChatMessage>

    /**
     * Deletes a specific message and its children.
     *
     * Upon successful deletion, the message is automatically removed from the internal
     * StateFlow, triggering updates to all observers.
     *
     * @param messageId The unique identifier of the message to delete
     * @return Either.Right with Unit on successful deletion, or Either.Left with RepositoryError on failure
     */
    suspend fun deleteMessage(messageId: Long): Either<RepositoryError, Unit>

    /**
     * Clears the cached messages for a specific session.
     *
     * This method resets the StateFlow for the specified session to Idle state,
     * useful when switching sessions or when a session is deleted.
     *
     * @param sessionId The ID of the session to clear messages for
     */
    fun clearMessagesForSession(sessionId: Long)

    /**
     * Loads messages for a specific session from the backend.
     *
     * This operation fetches the latest message data and updates the internal StateFlow.
     * If a load operation is already in progress for the session, this method returns immediately
     * without starting a duplicate operation.
     *
     * @param sessionId The ID of the session to load messages for
     * @return Either.Right with Unit on successful load, or Either.Left with RepositoryError on failure
     */
    suspend fun loadMessagesForSession(sessionId: Long): Either<RepositoryError, Unit>
}
