package eu.torvian.chatbot.app.repository

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.service.api.ModelApi
import eu.torvian.chatbot.common.models.AddModelRequest
import eu.torvian.chatbot.common.models.ApiKeyStatusResponse
import eu.torvian.chatbot.common.models.LLMModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * Default implementation of [ModelRepository] that manages LLM model configurations.
 *
 * This repository maintains an internal cache of model data using [MutableStateFlow] and
 * provides reactive updates to all observers. It delegates API operations to the injected
 * [ModelApi] and handles comprehensive error management through [RepositoryError].
 *
 * The repository ensures data consistency by automatically updating the internal StateFlow
 * whenever successful CRUD operations occur, eliminating the need for manual cache invalidation.
 *
 * @property modelApi The API client for model-related operations
 */
class DefaultModelRepository(
    private val modelApi: ModelApi
) : ModelRepository {

    private val _models = MutableStateFlow<DataState<RepositoryError, List<LLMModel>>>(DataState.Idle)
    override val models: StateFlow<DataState<RepositoryError, List<LLMModel>>> = _models.asStateFlow()

    // Cache for individual detailed model flows, guarded by a Mutex for thread safety
    private val _modelDetailsFlowsMutex = Mutex()
    private val _modelDetailsFlows: MutableMap<Long, MutableStateFlow<DataState<RepositoryError, LLMModel>>> =
        mutableMapOf()

    override suspend fun getModelFlow(modelId: Long): StateFlow<DataState<RepositoryError, LLMModel>> {
        return _modelDetailsFlowsMutex.withLock {
            _modelDetailsFlows.getOrPut(modelId) {
                // If not found, create a new flow initialized to Idle
                MutableStateFlow(DataState.Idle)
            }.asStateFlow()
        }
    }

    override suspend fun loadModels(): Either<RepositoryError, Unit> {
        // Prevent duplicate loading operations
        if (_models.value.isLoading) return Unit.right()

        _models.update { DataState.Loading }

        return modelApi.getAllModels()
            .map { modelList ->
                _models.update { DataState.Success(modelList) }
            }
            .mapLeft { apiResourceError ->
                val repositoryError = apiResourceError.toRepositoryError("Failed to load models")
                _models.update { DataState.Error(repositoryError) }
                repositoryError
            }
    }

    override suspend fun loadModelDetails(modelId: Long): Either<RepositoryError, LLMModel> {
        // Get or create the MutableStateFlow for this modelId, atomically.
        // This ensures the flow exists before we try to update it.
        val modelFlow = _modelDetailsFlowsMutex.withLock {
            _modelDetailsFlows.getOrPut(modelId) {
                MutableStateFlow(DataState.Idle)
            }
        }

        // Update the specific model flow to Loading (this operation on MutableStateFlow is atomic)
        modelFlow.update { DataState.Loading }

        return modelApi.getModelById(modelId)
            .map { model ->
                modelFlow.update { DataState.Success(model) }
                // Also update the model in the main list if it exists
                updateModelsState { list -> list.map { if (it.id == model.id) model else it } }
                model
            }
            .mapLeft { apiResourceError ->
                val repositoryError = apiResourceError.toRepositoryError("Failed to load model details")
                modelFlow.update { DataState.Error(repositoryError) }
                repositoryError
            }
    }

    override suspend fun addModel(request: AddModelRequest): Either<RepositoryError, LLMModel> {
        return modelApi.addModel(request)
            .map { newModel ->
                updateModelsState { it + newModel }

                // Get or create the flow for this new model and set its state to Success
                _modelDetailsFlowsMutex.withLock {
                    val flow = _modelDetailsFlows.getOrPut(newModel.id) {
                        MutableStateFlow(DataState.Idle)
                    }
                    flow.value = DataState.Success(newModel)
                }
                newModel
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to add model")
            }
    }

    override suspend fun getModelById(modelId: Long): Either<RepositoryError, LLMModel> {
        return modelApi.getModelById(modelId)
            .map { model ->
                updateModelsState { list -> list.map { if (it.id == model.id) model else it } }

                // Update the individual model flow if it exists
                _modelDetailsFlowsMutex.withLock {
                    _modelDetailsFlows[modelId]?.value = DataState.Success(model)
                }
                model
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to get model by ID")
            }
    }

    override suspend fun updateModel(model: LLMModel): Either<RepositoryError, Unit> {
        return modelApi.updateModel(model)
            .map {
                updateModelsState { list -> list.map { if (it.id == model.id) model else it } }

                // Update the individual model flow if it exists
                _modelDetailsFlowsMutex.withLock {
                    _modelDetailsFlows[model.id]?.value = DataState.Success(model)
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update model")
            }
    }

    override suspend fun deleteModel(modelId: Long): Either<RepositoryError, Unit> {
        return modelApi.deleteModel(modelId)
            .map {
                updateModelsState { list -> list.filter { it.id != modelId } }

                // Remove the individual model flow from cache
                _modelDetailsFlowsMutex.withLock {
                    _modelDetailsFlows.remove(modelId)
                }
                Unit
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to delete model")
            }
    }

    override suspend fun getModelApiKeyStatus(modelId: Long): Either<RepositoryError, ApiKeyStatusResponse> {
        return modelApi.getModelApiKeyStatus(modelId)
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to get model API key status")
            }
    }

    /**
     * Updates the internal StateFlow of models using the provided transformation.
     *
     * @param transform A function that takes the current list of models and returns an updated list.
     */
    private fun updateModelsState(transform: (List<LLMModel>) -> List<LLMModel>) {
        _models.update { currentState ->
            when (currentState) {
                is DataState.Success -> DataState.Success(transform(currentState.data))
                else -> currentState
            }
        }
    }
}
