package eu.torvian.chatbot.app.repository

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.service.api.SessionApi
import eu.torvian.chatbot.common.models.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * Default implementation of [SessionRepository] that manages chat sessions.
 *
 * This repository maintains an internal cache of session data using [MutableStateFlow] and
 * provides reactive updates to all observers. It delegates API operations to the injected
 * [SessionApi] and handles comprehensive error management through [RepositoryError].
 *
 * The repository ensures data consistency by automatically updating the internal StateFlow
 * whenever successful CRUD operations occur, eliminating the need for manual cache invalidation.
 *
 * @property sessionApi The API client for session-related operations
 */
class DefaultSessionRepository(
    private val sessionApi: SessionApi
) : SessionRepository {
    private val _sessions = MutableStateFlow<DataState<RepositoryError, List<ChatSessionSummary>>>(DataState.Idle)
    override val sessions: StateFlow<DataState<RepositoryError, List<ChatSessionSummary>>> = _sessions.asStateFlow()

    // Cache for individual detailed session flows, guarded by a Mutex for thread safety
    private val _sessionDetailsFlowsMutex = Mutex()
    private val _sessionDetailsFlows: MutableMap<Long, MutableStateFlow<DataState<RepositoryError, ChatSession>>> =
        mutableMapOf()

    override suspend fun getSessionDetailsFlow(sessionId: Long): StateFlow<DataState<RepositoryError, ChatSession>> {
        return _sessionDetailsFlowsMutex.withLock {
            _sessionDetailsFlows.getOrPut(sessionId) {
                // If not found, create a new flow initialized to Idle
                MutableStateFlow(DataState.Idle)
            }.asStateFlow()
        }
    }

    override suspend fun loadSessions(): Either<RepositoryError, Unit> {
        // Prevent duplicate loading operations
        if (_sessions.value.isLoading) return Unit.right()
        _sessions.update { DataState.Loading }
        return sessionApi.getAllSessions()
            .map { sessionList ->
                _sessions.update { DataState.Success(sessionList) }
            }
            .mapLeft { apiResourceError ->
                val repositoryError = apiResourceError.toRepositoryError("Failed to load sessions")
                _sessions.update { DataState.Error(repositoryError) }
                repositoryError
            }
    }

    override suspend fun createSession(request: CreateSessionRequest): Either<RepositoryError, ChatSession> {
        return sessionApi.createSession(request)
            .map { newSession ->
                // Add the new session to the internal list of summaries
                _sessions.update { currentState ->
                    when (currentState) {
                        is DataState.Success -> {
                            val newSummary = ChatSessionSummary(
                                id = newSession.id,
                                name = newSession.name,
                                groupId = newSession.groupId,
                                createdAt = newSession.createdAt,
                                updatedAt = newSession.updatedAt
                            )
                            val updatedSessions = currentState.data + newSummary
                            DataState.Success(updatedSessions)
                        }

                        else -> currentState // Keep other states (Loading, Error) unchanged
                    }
                }

                // Get or create the flow for this new session and set its state to Success
                _sessionDetailsFlowsMutex.withLock {
                    val flow = _sessionDetailsFlows.getOrPut(newSession.id) {
                        MutableStateFlow(DataState.Idle)
                    }
                    flow.value = DataState.Success(newSession)
                }
                newSession
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to create session")
            }
    }

    override suspend fun loadSessionDetails(sessionId: Long): Either<RepositoryError, ChatSession> {
        // Get or create the MutableStateFlow for this sessionId, atomically.
        // This ensures the flow exists before we try to update it.
        val sessionFlow = _sessionDetailsFlowsMutex.withLock {
            _sessionDetailsFlows.getOrPut(sessionId) {
                MutableStateFlow(DataState.Idle)
            }
        }

        // Update the specific session flow to Loading (this operation on MutableStateFlow is atomic)
        sessionFlow.update { DataState.Loading }

        return sessionApi.getSessionDetails(sessionId)
            .map { sessionDetails ->
                sessionFlow.update { DataState.Success(sessionDetails) }
                sessionDetails
            }
            .mapLeft { apiResourceError ->
                val repositoryError = apiResourceError.toRepositoryError("Failed to load session details")
                sessionFlow.update { DataState.Error(repositoryError) }
                repositoryError
            }
    }

    override suspend fun deleteSession(sessionId: Long): Either<RepositoryError, Unit> {
        return sessionApi.deleteSession(sessionId)
            .map {
                // Remove the session from the internal list of summaries
                _sessions.update { currentState ->
                    when (currentState) {
                        is DataState.Success -> {
                            val filteredSessions = currentState.data.filter { it.id != sessionId }
                            DataState.Success(filteredSessions)
                        }

                        else -> currentState // Keep other states unchanged
                    }
                }

                // Remove the session's details flow from the cache, atomically
                _sessionDetailsFlowsMutex.withLock<Unit> {
                    _sessionDetailsFlows.remove(sessionId)
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to delete session")
            }
    }

    override suspend fun updateSessionName(
        sessionId: Long,
        request: UpdateSessionNameRequest
    ): Either<RepositoryError, Unit> {
        return sessionApi.updateSessionName(sessionId, request)
            .map {
                // Update summary list
                updateSessionInList(sessionId) { session ->
                    session.copy(name = request.name)
                }
                // Update details cache if present.
                updateSessionDetailsInCache(sessionId) { session ->
                    session.copy(name = request.name)
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update session name")
            }
    }

    override suspend fun updateSessionModel(
        sessionId: Long,
        request: UpdateSessionModelRequest
    ): Either<RepositoryError, Unit> {
        return sessionApi.updateSessionModel(sessionId, request)
            .map {
                updateSessionDetailsInCache(sessionId) { session ->
                    session.copy(currentModelId = request.modelId)
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update session model")
            }
    }

    override suspend fun updateSessionSettings(
        sessionId: Long,
        request: UpdateSessionSettingsRequest
    ): Either<RepositoryError, Unit> {
        return sessionApi.updateSessionSettings(sessionId, request)
            .map {
                updateSessionDetailsInCache(sessionId) { session ->
                    session.copy(currentSettingsId = request.settingsId)
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update session settings")
            }
    }

    override suspend fun updateSessionLeafMessage(
        sessionId: Long,
        request: UpdateSessionLeafMessageRequest
    ): Either<RepositoryError, Unit> {
        return sessionApi.updateSessionLeafMessage(sessionId, request)
            .map {
                updateSessionDetailsInCache(sessionId) { session ->
                    session.copy(currentLeafMessageId = request.leafMessageId)
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update session leaf message")
            }
    }

    override suspend fun updateSessionGroup(
        sessionId: Long,
        request: UpdateSessionGroupRequest
    ): Either<RepositoryError, Unit> {
        return sessionApi.updateSessionGroup(sessionId, request)
            .map {
                // Update summary list
                updateSessionInList(sessionId) { session ->
                    session.copy(groupId = request.groupId)
                }
                // Update details cache if present, as ChatSession also has groupId
                updateSessionDetailsInCache(sessionId) { session ->
                    session.copy(groupId = request.groupId)
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update session group")
            }
    }

    /**
     * Helper to update a ChatSessionSummary in the main `_sessions` StateFlow.
     * Only applies the update if the current state is DataState.Success.
     */
    private fun updateSessionInList(sessionId: Long, updateFunction: (ChatSessionSummary) -> ChatSessionSummary) {
        _sessions.update { currentState ->
            when (currentState) {
                is DataState.Success -> {
                    val updatedSessions = currentState.data.map { session ->
                        if (session.id == sessionId) updateFunction(session) else session
                    }
                    DataState.Success(updatedSessions)
                }

                else -> currentState // Keep other states (Loading, Error) unchanged
            }
        }
    }

    /**
     * Helper to update a specific session's details flow in the `_sessionDetailsFlows` cache.
     * Only applies the update if the current state for that session ID is DataState.Success.
     * This function is safe as it updates an individual MutableStateFlow, which is inherently atomic.
     * Accessing the map for retrieval does not require a lock here because:
     * 1. The map itself is guarded by a mutex for structural changes (add/remove).
     * 2. If an item is concurrently removed, `get(sessionId)` will return null, and the `?.update`
     *    call will safely do nothing, which is the desired behavior.
     */
    private fun updateSessionDetailsInCache(sessionId: Long, updateFunction: (ChatSession) -> ChatSession) {
        _sessionDetailsFlows[sessionId]?.update { currentState ->
            when (currentState) {
                is DataState.Success -> DataState.Success(updateFunction(currentState.data))
                else -> currentState // Keep other states (Loading, Error) unchanged for the individual flow
            }
        }
    }
}