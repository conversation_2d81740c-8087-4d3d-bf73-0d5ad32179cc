package eu.torvian.chatbot.app.repository

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.service.api.SettingsApi
import eu.torvian.chatbot.common.models.ModelSettings
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * Default implementation of [SettingsRepository] that manages model settings profiles.
 *
 * This repository maintains an internal cache of settings data using [MutableStateFlow] and
 * provides reactive updates to all observers. It delegates API operations to the injected
 * [SettingsApi] and handles comprehensive error management through [RepositoryError].
 *
 * The repository ensures data consistency by automatically updating the internal StateFlow
 * whenever successful CRUD operations occur, eliminating the need for manual cache invalidation.
 *
 * @property settingsApi The API client for settings-related operations
 */
class DefaultSettingsRepository(
    private val settingsApi: SettingsApi
) : SettingsRepository {

    private val _settings = MutableStateFlow<DataState<RepositoryError, List<ModelSettings>>>(DataState.Idle)
    override val settings: StateFlow<DataState<RepositoryError, List<ModelSettings>>> = _settings.asStateFlow()

    // Cache for individual detailed settings flows, guarded by a Mutex for thread safety
    private val _settingsDetailsFlowsMutex = Mutex()
    private val _settingsDetailsFlows: MutableMap<Long, MutableStateFlow<DataState<RepositoryError, ModelSettings>>> =
        mutableMapOf()

    override suspend fun getSettingsFlow(settingsId: Long): StateFlow<DataState<RepositoryError, ModelSettings>> {
        return _settingsDetailsFlowsMutex.withLock {
            _settingsDetailsFlows.getOrPut(settingsId) {
                // If not found, create a new flow initialized to Idle
                MutableStateFlow(DataState.Idle)
            }.asStateFlow()
        }
    }

    // TODO: Extend Settings API to load all settings at once.
    override suspend fun loadSettings(): Either<RepositoryError, Unit> {
        // Prevent duplicate loading operations
        if (_settings.value.isLoading) return Unit.right()

        _settings.update { DataState.Loading }

        // Note: Since SettingsApi doesn't have a getAllSettings method,
        // we'll need to implement this differently or modify the approach
        // For now, we'll return success with empty list and rely on loadSettingsByModelId
        _settings.update { currentState -> DataState.Success(currentState.dataOrNull ?: emptyList()) }
        return Unit.right()
    }

    override suspend fun loadSettingsDetails(settingsId: Long): Either<RepositoryError, ModelSettings> {
        // Get or create the MutableStateFlow for this settingsId, atomically.
        // This ensures the flow exists before we try to update it.
        val settingsFlow = _settingsDetailsFlowsMutex.withLock {
            _settingsDetailsFlows.getOrPut(settingsId) {
                MutableStateFlow(DataState.Idle)
            }
        }

        // Update the specific settings flow to Loading (this operation on MutableStateFlow is atomic)
        settingsFlow.update { DataState.Loading }

        return settingsApi.getSettingsById(settingsId)
            .map { settings ->
                settingsFlow.update { DataState.Success(settings) }
                // Also update the settings in the main list if it exists
                updateSettingsState { list -> list.map { if (it.id == settings.id) settings else it } }
                settings
            }
            .mapLeft { apiResourceError ->
                val repositoryError = apiResourceError.toRepositoryError("Failed to load settings details")
                settingsFlow.update { DataState.Error(repositoryError) }
                repositoryError
            }
    }

    override suspend fun loadSettingsByModelId(modelId: Long): Either<RepositoryError, List<ModelSettings>> {
        return settingsApi.getSettingsByModelId(modelId)
            .map { settingsList ->
                updateSettingsState { current ->
                    val filteredSettings = current.filter { it.modelId != modelId }
                    filteredSettings + settingsList
                }
                settingsList
            }
            .mapLeft { apiResourceError ->
                val repositoryError = apiResourceError.toRepositoryError("Failed to load settings by model ID")
                _settings.update { DataState.Error(repositoryError) }
                repositoryError
            }
    }

    override suspend fun addModelSettings(settings: ModelSettings): Either<RepositoryError, ModelSettings> {
        return settingsApi.addModelSettings(settings)
            .map { newSettings ->
                updateSettingsState { it + newSettings }

                // Get or create the flow for this new settings and set its state to Success
                _settingsDetailsFlowsMutex.withLock {
                    val flow = _settingsDetailsFlows.getOrPut(newSettings.id) {
                        MutableStateFlow(DataState.Idle)
                    }
                    flow.value = DataState.Success(newSettings)
                }
                newSettings
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to add model settings")
            }
    }

    override suspend fun getSettingsById(settingsId: Long): Either<RepositoryError, ModelSettings> {
        return settingsApi.getSettingsById(settingsId)
            .map { settings ->
                updateSettingsState { list -> list.map { if (it.id == settings.id) settings else it } }

                // Update the individual settings flow if it exists
                _settingsDetailsFlowsMutex.withLock {
                    _settingsDetailsFlows[settingsId]?.value = DataState.Success(settings)
                }
                settings
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to get settings by ID")
            }
    }

    override suspend fun updateSettings(settings: ModelSettings): Either<RepositoryError, Unit> {
        return settingsApi.updateSettings(settings)
            .map {
                updateSettingsState { list -> list.map { if (it.id == settings.id) settings else it } }

                // Update the individual settings flow if it exists
                _settingsDetailsFlowsMutex.withLock {
                    _settingsDetailsFlows[settings.id]?.value = DataState.Success(settings)
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update settings")
            }
    }

    override suspend fun deleteSettings(settingsId: Long): Either<RepositoryError, Unit> {
        return settingsApi.deleteSettings(settingsId)
            .map {
                updateSettingsState { list -> list.filter { it.id != settingsId } }

                // Remove the individual settings flow from cache
                _settingsDetailsFlowsMutex.withLock {
                    _settingsDetailsFlows.remove(settingsId)
                }
                Unit
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to delete settings")
            }
    }

    /**
     * Updates the internal StateFlow of settings using the provided transformation.
     *
     * @param transform A function that takes the current list of settings and returns an updated list.
     */
    private fun updateSettingsState(transform: (List<ModelSettings>) -> List<ModelSettings>) {
        _settings.update { currentState ->
            when (currentState) {
                is DataState.Success -> DataState.Success(transform(currentState.data))
                else -> currentState
            }
        }
    }
}
