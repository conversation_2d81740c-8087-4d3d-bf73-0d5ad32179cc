package eu.torvian.chatbot.app.repository

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatStreamEvent
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import eu.torvian.chatbot.common.models.UpdateMessageRequest
import kotlinx.coroutines.flow.*

/**
 * Default implementation of [ChatRepository] that manages chat messages and streaming operations.
 *
 * This repository maintains an internal cache of message data using [MutableStateFlow] organized
 * by session ID and provides reactive updates to all observers. It delegates API operations to the
 * injected [ChatA<PERSON>] and handles comprehensive error management through [RepositoryError].
 *
 * The repository ensures data consistency by automatically updating the internal StateFlow
 * whenever successful CRUD operations occur, eliminating the need for manual cache invalidation.
 *
 * @property chatApi The API client for chat-related operations
 */
class DefaultChatRepository(
    private val chatApi: ChatApi
) : ChatRepository {

    // Internal cache of messages organized by session ID
    private val _messagesBySession =
        MutableStateFlow<Map<Long, DataState<RepositoryError, List<ChatMessage>>>>(emptyMap())

    // Cache of individual StateFlows for each session
    private val sessionStateFlows = mutableMapOf<Long, StateFlow<DataState<RepositoryError, List<ChatMessage>>>>()

    override fun getMessagesForSession(sessionId: Long): StateFlow<DataState<RepositoryError, List<ChatMessage>>> {
        return sessionStateFlows.getOrPut(sessionId) {
            _messagesBySession.map { sessionMap ->
                sessionMap[sessionId] ?: DataState.Idle
            }.stateIn(
                // fixme: GlobalScope is a hack to make this work. Should be a custom scope.
                scope = kotlinx.coroutines.GlobalScope,
                started = SharingStarted.WhileSubscribed(),
                initialValue = DataState.Idle
            )
        }
    }

    override suspend fun loadMessagesForSession(sessionId: Long): Either<RepositoryError, Unit> {
        // Check if already loading for this session
        val currentState = _messagesBySession.value[sessionId]
        if (currentState?.isLoading == true) return Unit.right()

        // Set loading state for this session
        _messagesBySession.update { sessionMap ->
            sessionMap + (sessionId to DataState.Loading)
        }

        // Note: In a real implementation, we would need a SessionApi method to get messages for a session
        // For now, we'll simulate this by returning an empty list
        // TODO: Add getMessagesForSession method to SessionApi or ChatApi

        // Simulate successful load with empty messages
        _messagesBySession.update { sessionMap ->
            sessionMap + (sessionId to DataState.Success(emptyList()))
        }

        return Unit.right()
    }

    override suspend fun processNewMessage(
        sessionId: Long,
        request: ProcessNewMessageRequest
    ): Either<RepositoryError, List<ChatMessage>> {
        return chatApi.processNewMessage(sessionId, request)
            .map { newMessages ->
                // Update the messages for this session by adding the new messages
                _messagesBySession.update { sessionMap ->
                    val currentState = sessionMap[sessionId]
                    val currentMessages = currentState?.dataOrNull ?: emptyList()
                    val updatedMessages = currentMessages + newMessages
                    sessionMap + (sessionId to DataState.Success(updatedMessages))
                }
                newMessages
            }
            .mapLeft { apiResourceError ->
                val repositoryError = apiResourceError.toRepositoryError("Failed to process new message")
                // Update error state for this session
                _messagesBySession.update { sessionMap ->
                    sessionMap + (sessionId to DataState.Error(repositoryError))
                }
                repositoryError
            }
    }

    override fun processNewMessageStreaming(
        sessionId: Long,
        request: ProcessNewMessageRequest
    ): Flow<Either<RepositoryError, ChatStreamEvent>> {
        return chatApi.processNewMessageStreaming(sessionId, request)
            .map { either ->
                either.mapLeft { apiResourceError ->
                    val repositoryError = apiResourceError.toRepositoryError("Failed to process streaming message")
                    // Update error state for this session
                    _messagesBySession.update { sessionMap ->
                        sessionMap + (sessionId to DataState.Error(repositoryError))
                    }
                    repositoryError
                }.map { streamEvent ->
                    // Handle different stream events and update the repository state accordingly
                    when (streamEvent) {
                        is ChatStreamEvent.AssistantMessageEnd -> {
                            // Update the messages for this session with the final messages
                            _messagesBySession.update { sessionMap ->
                                val currentState = sessionMap[sessionId]
                                val currentMessages = currentState?.dataOrNull ?: emptyList()
                                // Remove temporary messages and add final ones
                                val filteredMessages = currentMessages.filter {
                                    it.id != streamEvent.tempMessageId && it.id != streamEvent.finalUserMessage.id
                                }
                                val updatedMessages =
                                    filteredMessages + streamEvent.finalUserMessage + streamEvent.finalAssistantMessage
                                sessionMap + (sessionId to DataState.Success(updatedMessages))
                            }
                        }
                        // Other stream events don't need to update the repository state
                        else -> { /* No action needed for other events */
                        }
                    }
                    streamEvent
                }
            }
    }

    override suspend fun updateMessageContent(
        messageId: Long,
        request: UpdateMessageRequest
    ): Either<RepositoryError, ChatMessage> {
        return chatApi.updateMessageContent(messageId, request)
            .map { updatedMessage ->
                // Find and update the message in all sessions
                _messagesBySession.update { sessionMap ->
                    sessionMap.mapValues { (_, dataState) ->
                        when (dataState) {
                            is DataState.Success -> {
                                val updatedMessages = dataState.data.map { message ->
                                    if (message.id == messageId) updatedMessage else message
                                }
                                DataState.Success(updatedMessages)
                            }

                            else -> dataState
                        }
                    }
                }
                updatedMessage
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update message content")
            }
    }

    override suspend fun deleteMessage(messageId: Long): Either<RepositoryError, Unit> {
        return chatApi.deleteMessage(messageId)
            .map {
                // Remove the message from all sessions
                _messagesBySession.update { sessionMap ->
                    sessionMap.mapValues { (_, dataState) ->
                        when (dataState) {
                            is DataState.Success -> {
                                val filteredMessages = dataState.data.filter { message ->
                                    message.id != messageId
                                }
                                DataState.Success(filteredMessages)
                            }

                            else -> dataState
                        }
                    }
                }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to delete message")
            }
    }

    override fun clearMessagesForSession(sessionId: Long) {
        _messagesBySession.update { sessionMap ->
            sessionMap + (sessionId to DataState.Idle)
        }
    }
}
